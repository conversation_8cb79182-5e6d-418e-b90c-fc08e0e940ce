package com.ruoyi.ads.controller;

import com.ruoyi.ads.domain.*;
import com.ruoyi.ads.service.*;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 广告API控制器
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/api")
public class AdsApiController extends BaseController
{
    @Autowired
    private IAdsClickTrackingService adsClickTrackingService;

    @Autowired
    private IAdsCallbackRecordService adsCallbackRecordService;

    @Autowired
    private IAdsPublisherOfferService adsPublisherOfferService;

    @Autowired
    private IAdsPartnerService adsPartnerService;

    @Autowired
    private IAdsInfoService adsInfoService;

    /**
     * 点击追踪API
     */
    @GetMapping("/click")
    @Anonymous
    public void click(@RequestParam("offer_id") Long offerId,
                     @RequestParam("key") String partnerKey,
                     @RequestParam(value = "advid", required = false) String deviceId,
                     @RequestParam(value = "sub1", required = false) String subParam1,
                     @RequestParam(value = "sub2", required = false) String subParam2,
                     @RequestParam(value = "sub3", required = false) String subParam3,
                     @RequestParam(value = "channel", required = false) String channel,
                     HttpServletRequest request,
                     HttpServletResponse response) throws Exception
    {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");

        logger.info("收到点击请求: offerId={}, partnerKey={}, deviceId={}, ip={}, userAgent={}, sub1={}, sub2={}, sub3={}, channel={}",
                   offerId, partnerKey, deviceId, clientIp, userAgent, subParam1, subParam2, subParam3, channel);

        try
        {
            // 检查是否为预检请求或重复请求
            if (isPreflightRequest(request)) {
                logger.debug("忽略预检请求: {}", request.getMethod());
                response.setStatus(HttpServletResponse.SC_OK);
                return;
            }

            // 使用专门的点击追踪服务处理请求
            String targetUrl = adsClickTrackingService.processClickRequest(
                offerId, partnerKey, deviceId, subParam1, subParam2, subParam3,
                channel, request);

            // 重定向到目标URL
            if (StringUtils.isNotEmpty(targetUrl))
            {
                logger.info("点击处理成功，重定向到: {}", targetUrl);
                response.sendRedirect(targetUrl);
            }
            else
            {
                logger.warn("目标URL为空: offerId={}, partnerKey={}", offerId, partnerKey);
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Target URL not configured");
            }
        }
        catch (IllegalArgumentException e)
        {
            logger.warn("点击追踪参数错误: offerId={}, partnerKey={}, error={}",
                       offerId, partnerKey, e.getMessage());
            redirectToErrorPage(response, "400", e.getMessage(), offerId, partnerKey, request);
        }
        catch (SecurityException e)
        {
            logger.warn("点击追踪权限错误: offerId={}, partnerKey={}, error={}",
                       offerId, partnerKey, e.getMessage());
            redirectToErrorPage(response, "403", e.getMessage(), offerId, partnerKey, request);
        }
        catch (Exception e)
        {
            logger.error("点击追踪处理失败: offerId={}, partnerKey={}", offerId, partnerKey, e);
            redirectToErrorPage(response, "500", "系统内部错误，请稍后重试", offerId, partnerKey, request);
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        String xRealIp = request.getHeader("X-Real-IP");
        String remoteAddr = request.getRemoteAddr();

        if (StringUtils.isNotEmpty(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        if (StringUtils.isNotEmpty(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        return remoteAddr;
    }

    /**
     * 检查是否为预检请求
     */
    private boolean isPreflightRequest(HttpServletRequest request) {
        return "OPTIONS".equalsIgnoreCase(request.getMethod()) ||
               "HEAD".equalsIgnoreCase(request.getMethod());
    }

    /**
     * 安装回调API
     */
    @GetMapping("/callback/install")
    @Anonymous
    public ApiResponse<String> installCallback(@RequestParam("click_id") String clickId,
                                    @RequestParam(value = "event_name", required = false) String eventName,
                                    @RequestParam(value = "event_value", required = false) String eventValue,
                                    HttpServletRequest request)
    {
        return processCallback(clickId, "install", eventName, eventValue, request);
    }

    /**
     * 事件回调API
     */
    @GetMapping("/callback/event")
    @Anonymous
    public ApiResponse<String> eventCallback(@RequestParam("click_id") String clickId,
                                  @RequestParam("event_name") String eventName,
                                  @RequestParam(value = "event_value", required = false) String eventValue,
                                  HttpServletRequest request)
    {
        return processCallback(clickId, "event", eventName, eventValue, request);
    }

    /**
     * 处理回调
     */
    private ApiResponse<String> processCallback(String clickId, String callbackType, String eventName,
                                     String eventValue, HttpServletRequest request)
    {
        try
        {
            // 使用点击追踪服务处理回调
            boolean success = adsClickTrackingService.processCallback(
                clickId, callbackType, eventName, eventValue, request);

            if (success)
            {
                return ApiResponse.success("Callback processed successfully");
            }
            else
            {
                return ApiResponse.error("Callback processing failed");
            }
        }
        catch (IllegalArgumentException e)
        {
            logger.warn("回调参数错误: {}", e.getMessage());
            return ApiResponse.badRequest(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("回调处理失败", e);
            return ApiResponse.error("Callback processing failed");
        }
    }

    /**
     * 获取点击统计
     */
    @GetMapping("/stats/clicks")
    @Anonymous
    public ApiResponse<Object> getClickStats(@RequestParam(value = "partner_key", required = false) String partnerKey,
                                   @RequestParam(value = "start_date", required = false) String startDate,
                                   @RequestParam(value = "end_date", required = false) String endDate)
    {
        try
        {
            // 使用点击追踪服务获取统计数据
            Map<String, Object> stats = adsClickTrackingService.getClickStats(partnerKey, startDate, endDate);
            return ApiResponse.success(stats);
        }
        catch (Exception e)
        {
            logger.error("获取点击统计失败", e);
            return ApiResponse.error("Failed to get click statistics");
        }
    }

    /**
     * 获取回调统计
     */
    @GetMapping("/stats/callbacks")
    @Anonymous
    public ApiResponse<Object> getCallbackStats(@RequestParam(value = "partner_key", required = false) String partnerKey,
                                      @RequestParam(value = "start_date", required = false) String startDate,
                                      @RequestParam(value = "end_date", required = false) String endDate)
    {
        try
        {
            // 这里可以实现具体的统计逻辑
            Long totalCallbacks = adsCallbackRecordService.countAdsCallbackRecord();
            return ApiResponse.success(new java.util.HashMap<String, Object>() {{
                put("total_callbacks", totalCallbacks);
                put("partner_key", partnerKey);
                put("start_date", startDate);
                put("end_date", endDate);
            }});
        }
        catch (Exception e)
        {
            logger.error("获取回调统计失败", e);
            return ApiResponse.error("Failed to get callback statistics");
        }
    }

    /**
     * 拉取offer列表API
     */
    @GetMapping("/offers")
    @Anonymous
    public List<Map<String, Object>> getOffers(@RequestParam("key") String partnerKey,
                                              @RequestParam(value = "country", required = false) String country)
    {
        try
        {
            // 验证合作伙伴
            AdsPartner partner = adsPartnerService.selectAdsPartnerByPartnerKey(partnerKey);
            if (partner == null || !partner.isPublisher())
            {
                return new ArrayList<>();
            }

            // 查询该开发者的广告配置
            AdsPublisherOffer queryOffer = new AdsPublisherOffer();
            queryOffer.setPublisherId(partner.getPartnerId());
            queryOffer.setStatus("0"); // 只返回正常状态的配置

            List<AdsPublisherOffer> offers = adsPublisherOfferService.selectAdsPublisherOfferListWithRelated(queryOffer);

            // 转换为指定格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (AdsPublisherOffer offer : offers)
            {
                // 如果指定了国家，进行过滤
                if (country != null && !country.isEmpty() && offer.getCountry() != null)
                {
                    if (!offer.getCountry().toLowerCase().contains(country.toLowerCase()))
                    {
                        continue;
                    }
                }

                AdsInfo adsInfo = getAdsInfo(offer.getAdsId());
                Map<String, Object> offerMap = getStringObjectMap(offer, adsInfo);
                result.add(offerMap);
            }

            return result;
        }
        catch (Exception e)
        {
            logger.error("拉取offer列表失败", e);
            return new ArrayList<>();
        }
    }

    private Map<String, Object> getStringObjectMap(AdsPublisherOffer offer, AdsInfo adsInfo) {
        Map<String, Object> offerMap = new HashMap<>();
        offerMap.put("offerId", offer.getOfferId());
        offerMap.put("offerName", offer.getAdsName());
        offerMap.put("appId", adsInfo.getAppId());
        offerMap.put("clickUrl", getStringValue(offer.getClickUrl()));
        offerMap.put("previewUrl", getStringValue(offer.getPreviewUrl()));
        offerMap.put("country", getStringValue(offer.getCountry()));
        offerMap.put("installCap", offer.getInstallCap());
        offerMap.put("category", getStringValue(adsInfo.getCategory()));
        offerMap.put("dailyCap", offer.getDailyCap());
        offerMap.put("payout", offer.getPayout());
        offerMap.put("kpi", getStringValue(adsInfo.getKpi()));
        offerMap.put("conversionEvent", getStringValue(adsInfo.getConversionEvent()));
        offerMap.put("remark", offer.getRemark());
        return offerMap;
    }

    /**
     * 获取广告信息（缓存优化）
     */
    private AdsInfo getAdsInfo(Long adsId)
    {
        // 这里可以添加缓存逻辑
        return adsInfoService.selectAdsInfoByAdsId(adsId);
    }

    /**
     * 获取字符串值，null转为空字符串
     */
    private String getStringValue(String value)
    {
        return value == null ? "" : value;
    }

    /**
     * 重定向到错误页面
     */
    private void redirectToErrorPage(HttpServletResponse response, String errorCode, String errorMessage,
                                   Long offerId, String partnerKey, HttpServletRequest request) throws Exception
    {
        // 生成请求ID用于追踪
        String requestId = request.getHeader("X-Request-ID");
        if (StringUtils.isEmpty(requestId)) {
            requestId = java.util.UUID.randomUUID().toString().replace("-", "");
        }

        // 构建错误页面URL
        StringBuilder errorUrl = new StringBuilder("/ads/error/click");
        errorUrl.append("?code=").append(java.net.URLEncoder.encode(errorCode, "UTF-8"));
        errorUrl.append("&msg=").append(java.net.URLEncoder.encode(errorMessage, "UTF-8"));
        errorUrl.append("&request_id=").append(requestId);

        if (offerId != null) {
            errorUrl.append("&offer_id=").append(offerId);
        }
        if (StringUtils.isNotEmpty(partnerKey)) {
            errorUrl.append("&partner_key=").append(java.net.URLEncoder.encode(partnerKey, "UTF-8"));
        }

        logger.info("重定向到错误页面: {}", errorUrl.toString());
        response.sendRedirect(errorUrl.toString());
    }
}
